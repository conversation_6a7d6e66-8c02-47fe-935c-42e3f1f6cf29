"""
Main entry point for Trading AI Bot
"""
import sys
import os
import logging
from datetime import datetime

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from utils.logger import setup_logger
from services.cli_interface import CLIInterface

def setup_directories():
    """Create necessary directories if they don't exist"""
    directories = [
        Config.DATA_PATH,
        Config.CHART_STORAGE_PATH,
        Config.LOGS_PATH,
        './data/excel'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def main():
    """Main function"""
    try:
        # Setup directories
        setup_directories()
        
        # Setup logging
        logger = setup_logger()
        logger.info("Starting Trading AI Bot...")
        
        # Initialize CLI interface
        cli = CLIInterface()
        cli.run()
        
    except KeyboardInterrupt:
        print("\n\nBot stopped by user.")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
