"""
Logging utility for Trading AI Bot
"""
import logging
import os
from datetime import datetime
from config import Config

def setup_logger(name='trading_ai_bot', log_file=None):
    """
    Setup logger with file and console handlers
    
    Args:
        name (str): Logger name
        log_file (str): Log file path (optional)
    
    Returns:
        logging.Logger: Configured logger
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, Config.LOG_LEVEL))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    if log_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(Config.LOGS_PATH, f'trading_bot_{timestamp}.log')
    
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(getattr(logging, Config.LOG_LEVEL))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger

def get_logger(name='trading_ai_bot'):
    """Get existing logger or create new one"""
    return logging.getLogger(name)
