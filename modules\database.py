"""
Database module for Trading AI Bot
Handles MySQL database connections and operations
"""
import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>
from contextlib import contextmanager
from typing import Optional, Dict, List, Any
import json
from datetime import datetime

from config import Config
from utils.logger import get_logger

logger = get_logger(__name__)

class DatabaseManager:
    """Database manager for Trading AI Bot"""
    
    def __init__(self):
        self.config = Config.DB_CONFIG
        self._connection = None
    
    def connect(self) -> bool:
        """
        Establish database connection
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self._connection = mysql.connector.connect(**self.config)
            if self._connection.is_connected():
                logger.info("Successfully connected to MySQL database")
                return True
        except Error as e:
            logger.error(f"Error connecting to MySQL: {e}")
            return False
        return False
    
    def disconnect(self):
        """Close database connection"""
        if self._connection and self._connection.is_connected():
            self._connection.close()
            logger.info("MySQL connection closed")
    
    @contextmanager
    def get_cursor(self):
        """Context manager for database cursor"""
        if not self._connection or not self._connection.is_connected():
            if not self.connect():
                raise Exception("Failed to connect to database")
        
        cursor = self._connection.cursor(dictionary=True)
        try:
            yield cursor
            self._connection.commit()
        except Exception as e:
            self._connection.rollback()
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            cursor.close()
    
    def create_tables(self):
        """Create all necessary tables"""
        tables = {
            'strategies': """
                CREATE TABLE IF NOT EXISTS strategies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    youtube_url VARCHAR(500) NOT NULL,
                    title VARCHAR(255),
                    transcript TEXT,
                    strategy_content TEXT,
                    extracted_rules JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_youtube_url (youtube_url(255))
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,
            
            'trading_signals': """
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    pair VARCHAR(10) NOT NULL,
                    signal_type ENUM('BUY', 'SELL') NOT NULL,
                    entry_price DECIMAL(10, 5),
                    stop_loss DECIMAL(10, 5),
                    take_profit DECIMAL(10, 5),
                    confidence_score DECIMAL(3, 2),
                    strategy_id INT,
                    chart_image_path VARCHAR(500),
                    analysis_data JSON,
                    status ENUM('ACTIVE', 'CLOSED', 'CANCELLED') DEFAULT 'ACTIVE',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (strategy_id) REFERENCES strategies(id),
                    INDEX idx_pair_date (pair, created_at),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,
            
            'trading_journal': """
                CREATE TABLE IF NOT EXISTS trading_journal (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    trade_id VARCHAR(50),
                    pair VARCHAR(10) NOT NULL,
                    trade_type ENUM('BUY', 'SELL') NOT NULL,
                    entry_price DECIMAL(10, 5),
                    exit_price DECIMAL(10, 5),
                    stop_loss DECIMAL(10, 5),
                    take_profit DECIMAL(10, 5),
                    lot_size DECIMAL(8, 2),
                    profit_loss DECIMAL(10, 2),
                    entry_time DATETIME,
                    exit_time DATETIME,
                    result ENUM('WIN', 'LOSS', 'BREAKEVEN'),
                    signal_id INT,
                    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (signal_id) REFERENCES trading_signals(id),
                    INDEX idx_pair_result (pair, result),
                    INDEX idx_entry_time (entry_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,
            
            'trade_evaluations': """
                CREATE TABLE IF NOT EXISTS trade_evaluations (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    journal_id INT NOT NULL,
                    loss_reason TEXT,
                    market_condition VARCHAR(100),
                    news_impact BOOLEAN DEFAULT FALSE,
                    strategy_violation BOOLEAN DEFAULT FALSE,
                    lessons_learned TEXT,
                    chart_analysis JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (journal_id) REFERENCES trading_journal(id),
                    INDEX idx_journal_id (journal_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,
            
            'economic_news': """
                CREATE TABLE IF NOT EXISTS economic_news (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    impact_level ENUM('LOW', 'MEDIUM', 'HIGH') NOT NULL,
                    currency VARCHAR(10),
                    event_time DATETIME,
                    actual_value VARCHAR(50),
                    forecast_value VARCHAR(50),
                    previous_value VARCHAR(50),
                    source VARCHAR(50),
                    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_event_time (event_time),
                    INDEX idx_impact_currency (impact_level, currency)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """
        }
        
        try:
            with self.get_cursor() as cursor:
                for table_name, create_sql in tables.items():
                    cursor.execute(create_sql)
                    logger.info(f"Table '{table_name}' created or verified")
            logger.info("All database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            raise

    # Strategy operations
    def save_strategy(self, youtube_url: str, title: str, transcript: str,
                     strategy_content: str, extracted_rules: Dict) -> int:
        """Save extracted strategy to database"""
        try:
            with self.get_cursor() as cursor:
                query = """
                    INSERT INTO strategies (youtube_url, title, transcript,
                                         strategy_content, extracted_rules)
                    VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(query, (
                    youtube_url, title, transcript,
                    strategy_content, json.dumps(extracted_rules)
                ))
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error saving strategy: {e}")
            raise

    def get_strategies(self) -> List[Dict]:
        """Get all strategies"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("SELECT * FROM strategies ORDER BY created_at DESC")
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Error fetching strategies: {e}")
            return []

    # Signal operations
    def save_signal(self, pair: str, signal_type: str, entry_price: float,
                   stop_loss: float, take_profit: float, confidence_score: float,
                   strategy_id: int, chart_image_path: str, analysis_data: Dict) -> int:
        """Save trading signal to database"""
        try:
            with self.get_cursor() as cursor:
                query = """
                    INSERT INTO trading_signals (pair, signal_type, entry_price,
                                               stop_loss, take_profit, confidence_score,
                                               strategy_id, chart_image_path, analysis_data)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(query, (
                    pair, signal_type, entry_price, stop_loss, take_profit,
                    confidence_score, strategy_id, chart_image_path,
                    json.dumps(analysis_data)
                ))
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error saving signal: {e}")
            raise

    def get_active_signals(self, pair: str = None) -> List[Dict]:
        """Get active trading signals"""
        try:
            with self.get_cursor() as cursor:
                if pair:
                    cursor.execute(
                        "SELECT * FROM trading_signals WHERE status = 'ACTIVE' AND pair = %s ORDER BY created_at DESC",
                        (pair,)
                    )
                else:
                    cursor.execute(
                        "SELECT * FROM trading_signals WHERE status = 'ACTIVE' ORDER BY created_at DESC"
                    )
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Error fetching active signals: {e}")
            return []

    # Journal operations
    def save_journal_entries(self, entries: List[Dict]) -> int:
        """Save multiple journal entries from Excel"""
        try:
            with self.get_cursor() as cursor:
                query = """
                    INSERT INTO trading_journal (trade_id, pair, trade_type, entry_price,
                                               exit_price, stop_loss, take_profit, lot_size,
                                               profit_loss, entry_time, exit_time, result)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                count = 0
                for entry in entries:
                    cursor.execute(query, (
                        entry.get('trade_id'), entry.get('pair'), entry.get('trade_type'),
                        entry.get('entry_price'), entry.get('exit_price'), entry.get('stop_loss'),
                        entry.get('take_profit'), entry.get('lot_size'), entry.get('profit_loss'),
                        entry.get('entry_time'), entry.get('exit_time'), entry.get('result')
                    ))
                    count += 1

                logger.info(f"Saved {count} journal entries")
                return count
        except Exception as e:
            logger.error(f"Error saving journal entries: {e}")
            raise

    def get_loss_trades(self, limit: int = 50) -> List[Dict]:
        """Get trades that resulted in losses"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM trading_journal WHERE result = 'LOSS' ORDER BY exit_time DESC LIMIT %s",
                    (limit,)
                )
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Error fetching loss trades: {e}")
            return []

    # Evaluation operations
    def save_trade_evaluation(self, journal_id: int, loss_reason: str,
                            market_condition: str, news_impact: bool,
                            strategy_violation: bool, lessons_learned: str,
                            chart_analysis: Dict) -> int:
        """Save trade evaluation"""
        try:
            with self.get_cursor() as cursor:
                query = """
                    INSERT INTO trade_evaluations (journal_id, loss_reason, market_condition,
                                                 news_impact, strategy_violation, lessons_learned,
                                                 chart_analysis)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(query, (
                    journal_id, loss_reason, market_condition, news_impact,
                    strategy_violation, lessons_learned, json.dumps(chart_analysis)
                ))
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error saving trade evaluation: {e}")
            raise

    # News operations
    def save_news(self, news_items: List[Dict]) -> int:
        """Save economic news"""
        try:
            with self.get_cursor() as cursor:
                query = """
                    INSERT INTO economic_news (title, description, impact_level, currency,
                                             event_time, actual_value, forecast_value,
                                             previous_value, source)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    actual_value = VALUES(actual_value),
                    scraped_at = CURRENT_TIMESTAMP
                """

                count = 0
                for news in news_items:
                    cursor.execute(query, (
                        news.get('title'), news.get('description'), news.get('impact_level'),
                        news.get('currency'), news.get('event_time'), news.get('actual_value'),
                        news.get('forecast_value'), news.get('previous_value'), news.get('source')
                    ))
                    count += 1

                logger.info(f"Saved {count} news items")
                return count
        except Exception as e:
            logger.error(f"Error saving news: {e}")
            raise

    def get_recent_high_impact_news(self, hours: int = 24) -> List[Dict]:
        """Get recent high impact news"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM economic_news
                    WHERE impact_level = 'HIGH'
                    AND event_time >= DATE_SUB(NOW(), INTERVAL %s HOUR)
                    ORDER BY event_time DESC
                """, (hours,))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Error fetching high impact news: {e}")
            return []
