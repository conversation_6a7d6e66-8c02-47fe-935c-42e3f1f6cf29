"""
Configuration settings for Trading AI Bot
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Main configuration class"""
    
    # API Keys
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    TRADINGVIEW_API_KEY = os.getenv('TRADINGVIEW_API_KEY')
    
    # Database Configuration
    DB_CONFIG = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'database': os.getenv('DB_NAME', 'trading_ai_bot'),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', ''),
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    # Application Settings
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    
    # File Paths
    CHART_STORAGE_PATH = os.getenv('CHART_STORAGE_PATH', './data/charts/')
    DATA_PATH = './data/'
    LOGS_PATH = './logs/'
    
    # News Settings
    NEWS_UPDATE_INTERVAL = int(os.getenv('NEWS_UPDATE_INTERVAL', 3600))
    MAX_NEWS_AGE = int(os.getenv('MAX_NEWS_AGE', 86400))
    
    # Trading Pairs
    SUPPORTED_PAIRS = [
        'XAUUSD', 'EURUSD', 'GBPUSD', 'USDJPY', 
        'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD'
    ]
    
    # Chart Settings
    CHART_TIMEFRAMES = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
    DEFAULT_TIMEFRAME = '1h'
    
    # News Sources
    NEWS_SOURCES = {
        'forexfactory': 'https://www.forexfactory.com/calendar',
        'investing': 'https://www.investing.com/economic-calendar/'
    }

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
